
import { useNavigate } from "react-router-dom";
import { useEffect } from "react";

const demoData = {
  brideName: "<PERSON><PERSON>",
  groomName: "<PERSON>",
  weddingDate: "2024-07-20",
  venue: "Munyonyo Commonwealth Resort",
  treasurerName: "<PERSON><PERSON><PERSON> Shadad",
  treasurerPhone: "+256 77 *** ***",
  specialMessage: "We are excited to celebrate our union with family and friends. Your support means the world to us as we begin this beautiful journey together.",
  theme: "royal"
};

const DemoPledge = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Navigate to the pledge card with demo data
    navigate('/pledge/demo', { state: { pledgeData: demoData }, replace: true });
  }, [navigate]);

  return null;
};

export default DemoPledge;