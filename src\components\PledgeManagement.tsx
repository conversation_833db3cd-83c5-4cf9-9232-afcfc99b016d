
import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useErrorHandler } from "@/hooks/use-error-handler";
import { supabase } from "@/integrations/supabase/client";
import type { Pledge, PaymentStatus } from "@/types/app";
import EditPledgeDialog from "./EditPledgeDialog";
import PaymentUpdateDialog from "./PaymentUpdateDialog";
import AddPledgeDialog from "./AddPledgeDialog";
import PledgeStats from "./PledgeStats";
import PledgeFilters from "./PledgeFilters";
import PledgeTable from "./PledgeTable";
import PledgeExportButton from "./PledgeExportButton";
import LoadingSpinner from "./ui/loading-spinner";

interface PledgeManagementProps {
  userId: string;
  onPledgeChange?: () => void;
}

const PledgeManagement = ({ userId, onPledgeChange }: PledgeManagementProps) => {
  const [pledges, setPledges] = useState<Pledge[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<PaymentStatus | "all">("all");
  const [editingPledge, setEditingPledge] = useState<Pledge | null>(null);
  const [updatingPayment, setUpdatingPayment] = useState<Pledge | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  
  const { toast } = useToast();
  const { handleSupabaseOperation } = useErrorHandler();

  const fetchPledges = useCallback(async () => {
    await handleSupabaseOperation(
      async () => {
        console.log('Fetching pledges for user:', userId);
        const { data, error } = await supabase
          .from('pledges')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Supabase operation failed:', error);
          throw error;
        }

        console.log('Successfully fetched pledges:', data?.length || 0);
        setPledges(data || []);
        setLoading(false);
      },
      {
        defaultMessage: "Failed to load pledges. Please try again."
      }
    );
  }, [userId, handleSupabaseOperation]);

  const refreshPledges = useCallback(async () => {
    await fetchPledges();
    onPledgeChange?.();
  }, [fetchPledges, onPledgeChange]);

  useEffect(() => {
    fetchPledges();
  }, [fetchPledges]);

  const handleEditPledge = async (pledgeId: string, updates: Partial<Pledge>) => {
    try {
      const { error } = await supabase
        .from('pledges')
        .update(updates)
        .eq('id', pledgeId);

      if (error) throw error;

      await refreshPledges();
      toast({
        title: "Pledge Updated",
        description: "Pledge has been updated successfully.",
      });
    } catch (error) {
      console.error('Error updating pledge:', error);
      toast({
        title: "Error",
        description: "Failed to update pledge. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleUpdatePayment = async (pledgeId: string, updates: { amount_paid: number; payment_status: PaymentStatus }) => {
    try {
      const { error } = await supabase
        .from('pledges')
        .update({
          ...updates,
          payment_date: updates.payment_status === 'completed' ? new Date().toISOString() : null,
          updated_at: new Date().toISOString()
        })
        .eq('id', pledgeId);

      if (error) throw error;

      await refreshPledges();
      toast({
        title: "Payment Updated",
        description: "Payment status has been updated successfully.",
      });
    } catch (error) {
      console.error('Error updating payment:', error);
      toast({
        title: "Error",
        description: "Failed to update payment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeletePledge = async (pledgeId: string) => {
    if (!confirm('Are you sure you want to delete this pledge? This action cannot be undone.')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('pledges')
        .delete()
        .eq('id', pledgeId);

      if (error) throw error;

      await refreshPledges();
      toast({
        title: "Pledge Deleted",
        description: "Pledge has been deleted successfully.",
      });
    } catch (error) {
      console.error('Error deleting pledge:', error);
      toast({
        title: "Error",
        description: "Failed to delete pledge. Please try again.",
        variant: "destructive",
      });
    }
  };

  const filteredPledges = pledges.filter(pledge => {
    const matchesSearch = pledge.guest_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pledge.guest_phone?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || pledge.payment_status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const totalPledged = pledges.reduce((sum, pledge) => sum + pledge.amount_pledged, 0);
  const totalPaid = pledges.reduce((sum, pledge) => sum + pledge.amount_paid, 0);
  const pendingAmount = totalPledged - totalPaid;

  if (loading) {
    return <div className="flex justify-center items-center min-h-64">Loading pledges...</div>;
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <LoadingSpinner variant="card" size="lg" text="Loading pledges..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PledgeStats
        totalPledged={totalPledged}
        totalPaid={totalPaid}
        pendingAmount={pendingAmount}
      />

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Pledge Management</CardTitle>
              <CardDescription>Manage all pledges and payment status</CardDescription>
            </div>
            <div className="flex gap-2">
              <PledgeExportButton
                pledges={filteredPledges}
                disabled={loading || pledges.length === 0}
              />
              <Button onClick={() => setShowAddDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Pledge
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <PledgeFilters
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            statusFilter={statusFilter}
            setStatusFilter={setStatusFilter}
          />

          <PledgeTable
            pledges={filteredPledges}
            onEditPledge={setEditingPledge}
            onDeletePledge={handleDeletePledge}
            onUpdatePayment={setUpdatingPayment}
          />
        </CardContent>
      </Card>

      <EditPledgeDialog
        pledge={editingPledge}
        open={!!editingPledge}
        onOpenChange={(open) => !open && setEditingPledge(null)}
        onSave={handleEditPledge}
      />

      <PaymentUpdateDialog
        pledge={updatingPayment}
        open={!!updatingPayment}
        onOpenChange={(open) => !open && setUpdatingPayment(null)}
        onSave={handleUpdatePayment}
      />

      <AddPledgeDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        onPledgeAdded={refreshPledges}
        userId={userId}
      />
    </div>
  );
};

export default PledgeManagement;
