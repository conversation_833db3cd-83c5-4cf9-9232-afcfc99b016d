
import { useNavigate } from "react-router-dom";
import { useEffect } from "react";

const demoData = {
  brideName: "<PERSON><PERSON><PERSON><PERSON>",
  groomName: "Tom<PERSON>",
  weddingDate: "2022-10-21",
  venue: "Serena Hotel",
  treasurerName: "<PERSON> Nakat<PERSON>",
  treasurer<PERSON><PERSON>: "+256 777 123 456",
  specialMessage: "We are excited to celebrate our union with family and friends. Your support means the world to us as we begin this beautiful journey together.",
  theme: "royal"
};

const DemoPledge = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Navigate to the pledge card with demo data
    navigate('/pledge/demo', { state: { pledgeData: demoData }, replace: true });
  }, [navigate]);

  return null;
};

export default DemoPledge;