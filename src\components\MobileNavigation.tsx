import { Button } from "@/components/ui/button";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetClose 
} from "@/components/ui/sheet";
import { Heart, User, LogOut, Shield, Menu, HelpCircle, Eye } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useAuthModal } from "@/contexts/AuthModalContext";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";

interface MobileNavigationProps {
  isAdmin: boolean;
  isSigningOut: boolean;
  onSignOut: () => void;
}

const MobileNavigation = ({ isAdmin, isSigningOut, onSignOut }: MobileNavigationProps) => {
  const navigate = useNavigate();
  const { openAuthModal } = useAuthModal();
  const { user, profile } = useAuth();

  const handleNavigation = (path: string, closeSheet?: () => void) => {
    navigate(path);
    if (closeSheet) closeSheet();
  };

  const handleAuthAction = (action: () => void, closeSheet?: () => void) => {
    action();
    if (closeSheet) closeSheet();
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden h-11 w-11"
          aria-label="Open navigation menu"
        >
          <Menu className="h-5 w-5" />
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="w-[300px] sm:w-[400px]">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <Heart className="h-6 w-6 text-pink-500" />
            PledgeForLove
          </SheetTitle>
        </SheetHeader>
        
        <div className="mt-8 space-y-4">
          {user ? (
            <>
              {/* User Info Section */}
              <div className="pb-4 border-b border-gray-200">
                <div className="text-sm font-medium text-gray-900">
                  Welcome, {isAdmin ? 'Administrator' : (profile?.treasurer_name || 'User')}
                </div>
                {profile && !isAdmin && (
                  <div className="text-xs text-gray-500 mt-1">
                    {profile.bride_name} & {profile.groom_name}
                  </div>
                )}
                {isAdmin && (
                  <div className="text-xs text-blue-600 mt-1">
                    System Administrator
                  </div>
                )}
              </div>

              {/* Navigation Items for Authenticated Users */}
              <div className="space-y-2">
                {!isAdmin && (
                  <SheetClose asChild>
                    <Button
                      variant="ghost"
                      className="w-full justify-start h-11"
                      onClick={() => handleNavigation('/dashboard')}
                    >
                      <Heart className="h-4 w-4 mr-3" />
                      Dashboard
                    </Button>
                  </SheetClose>
                )}

                {!isAdmin && (
                  <SheetClose asChild>
                    <Button
                      variant="ghost"
                      className="w-full justify-start h-11"
                      onClick={() => handleNavigation('/profile')}
                    >
                      <User className="h-4 w-4 mr-3" />
                      Profile
                    </Button>
                  </SheetClose>
                )}

                {isAdmin && (
                  <SheetClose asChild>
                    <Button
                      variant="ghost"
                      className="w-full justify-start h-11 text-blue-600 hover:text-blue-700"
                      onClick={() => handleNavigation('/admin')}
                    >
                      <Shield className="h-4 w-4 mr-3" />
                      Admin Panel
                    </Button>
                  </SheetClose>
                )}

                <SheetClose asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-start h-11"
                    onClick={() => handleNavigation('/faq')}
                  >
                    <HelpCircle className="h-4 w-4 mr-3" />
                    FAQ
                  </Button>
                </SheetClose>

                <SheetClose asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-start h-11"
                    onClick={() => handleNavigation('/demo-pledge')}
                  >
                    <Eye className="h-4 w-4 mr-3" />
                    View Demo
                  </Button>
                </SheetClose>
              </div>

              {/* Sign Out Button */}
              <div className="pt-4 border-t border-gray-200">
                <SheetClose asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-start h-11 text-red-600 hover:text-red-700 hover:bg-red-50"
                    onClick={onSignOut}
                    disabled={isSigningOut}
                  >
                    <LogOut className="h-4 w-4 mr-3" />
                    {isSigningOut ? "Signing Out..." : "Sign Out"}
                  </Button>
                </SheetClose>
              </div>
            </>
          ) : (
            <>
              {/* Navigation Items for Unauthenticated Users */}
              <div className="space-y-2">
                <SheetClose asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-start h-11"
                    onClick={() => handleNavigation('/faq')}
                  >
                    <HelpCircle className="h-4 w-4 mr-3" />
                    FAQ
                  </Button>
                </SheetClose>

                <SheetClose asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-start h-11"
                    onClick={() => handleNavigation('/demo-pledge')}
                  >
                    <Eye className="h-4 w-4 mr-3" />
                    View Demo
                  </Button>
                </SheetClose>
              </div>

              {/* Auth Actions */}
              <div className="pt-4 border-t border-gray-200 space-y-2">
                <SheetClose asChild>
                  <Button
                    className="w-full h-11"
                    onClick={() => handleAuthAction(() => openAuthModal('signin'))}
                  >
                    Sign In
                  </Button>
                </SheetClose>

                <SheetClose asChild>
                  <Button
                    variant="outline"
                    className="w-full h-11"
                    onClick={() => handleAuthAction(() => openAuthModal('signup'))}
                  >
                    Create Account
                  </Button>
                </SheetClose>
              </div>
            </>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default MobileNavigation;
