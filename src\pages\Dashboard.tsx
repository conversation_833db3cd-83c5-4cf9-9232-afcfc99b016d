import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Calendar, MapPin, User, Heart, Eye, Share2, RefreshCw } from "lucide-react";
import type { Pledge } from "@/types/app";
import PledgeManagement from "@/components/PledgeManagement";
import ProfileManagement from "@/components/ProfileManagement";
import LoadingSpinner from "@/components/ui/loading-spinner";
import { useSEO } from "@/utils/seo";
import { BreadcrumbStructuredData } from "@/components/StructuredData";
import { useAuth } from "@/contexts/AuthContext";
import { isCurrentUserAdmin } from "@/utils/adminAuth";

const Dashboard = () => {
  const { user, profile, refreshProfile } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [recentPledges, setRecentPledges] = useState<Pledge[]>([]);
  const navigate = useNavigate();
  const { toast } = useToast();

  // SEO optimization for dashboard page
  useSEO('dashboard');

  const [activeTab, setActiveTab] = useState<'overview' | 'pledges' | 'profile'>('overview');

  const handleTabChange = (tab: 'overview' | 'pledges' | 'profile') => {
    setActiveTab(tab);
    // Refresh recent pledges when switching to overview tab
    if (tab === 'overview' && profile && !isAdmin) {
      fetchRecentPledgesForProfile();
    }
  };
  const [pledgeCardModalOpen, setPledgeCardModalOpen] = useState(false);
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);

  const fetchRecentPledges = useCallback(async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('pledges')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      setRecentPledges(data || []);
    } catch (error) {
      console.error('Error fetching recent pledges:', error);
      toast({
        title: "Error",
        description: "Failed to load recent pledges. Please try again.",
        variant: "destructive",
      });
    }
  }, [toast]);

  // Fetch recent pledges when profile is available
  const fetchRecentPledgesForProfile = useCallback(async () => {
    if (!profile) return;

    try {
      setLoading(true);
      setError(null);
      await fetchRecentPledges(profile.id);
    } catch (error: unknown) {
      console.error('Error fetching recent pledges:', error);
      setError(error);
      toast({
        title: "Error",
        description: "Failed to load recent pledges. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [profile, fetchRecentPledges, toast]);

  useEffect(() => {
    // Check if user is admin and redirect to admin panel
    const checkAdminStatus = async () => {
      if (user) {
        try {
          const adminStatus = await isCurrentUserAdmin();
          setIsAdmin(adminStatus);
          if (adminStatus) {
            // Redirect admins to admin panel instead of dashboard
            navigate('/admin');
            return;
          }
        } catch (error) {
          console.error('Error checking admin status:', error);
          setIsAdmin(false);
        }
      }
    };

    checkAdminStatus();
  }, [user, navigate]);

  useEffect(() => {
    // Fetch recent pledges when profile becomes available (only for non-admin users)
    if (profile && !isAdmin) {
      fetchRecentPledgesForProfile();
    }
  }, [profile, fetchRecentPledgesForProfile, isAdmin]);

  // Refresh recent pledges when page becomes visible (to catch pledges added from other tabs/devices)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && profile && !isAdmin && activeTab === 'overview') {
        fetchRecentPledgesForProfile();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [profile, isAdmin, activeTab, fetchRecentPledgesForProfile]);

  if (loading) {
    return (
      <div className="min-h-64">
        <LoadingSpinner variant="card" size="lg" text="Loading your dashboard..." />
      </div>
    );
  }

  if (error || !profile) {
    return <div className="flex justify-center items-center min-h-64">Error loading dashboard. Please try again.</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Breadcrumb Structured Data */}
      <BreadcrumbStructuredData
        items={[
          { name: "Home", url: "https://p4love.com" },
          { name: "Dashboard", url: "https://p4love.com/dashboard" }
        ]}
      />
      {/* Header Section */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-sm sm:text-base text-gray-600">Welcome, {profile.treasurer_name}! Manage your wedding pledges and profile settings here.</p>
          </div>
          <Dialog open={pledgeCardModalOpen} onOpenChange={setPledgeCardModalOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2 w-full sm:w-auto">
                <Heart className="h-4 w-4" />
                Pledge Card
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5 text-pink-500" />
                  Your Pledge Card
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <p className="text-gray-600">
                  Manage and share your wedding pledge card with guests
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    onClick={() => {
                      navigate('/create-pledge');
                      setPledgeCardModalOpen(false);
                    }}
                    className="flex-1"
                  >
                    <Heart className="h-4 w-4 mr-2" />
                    {profile.theme && profile.special_message ? 'Edit Pledge Card' : 'Create Pledge Card'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      navigate(`/pledge/${user?.id}`);
                      setPledgeCardModalOpen(false);
                    }}
                    className="flex-1"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Card
                  </Button>
                  {profile.is_public && (
                    <Button
                      variant="outline"
                      onClick={() => {
                        if (user?.id) {
                          const pledgeUrl = `${window.location.origin}/pledge/${user.id}`;
                          navigator.clipboard.writeText(pledgeUrl);
                          toast({
                            title: "Link Copied!",
                            description: "Share this link with your guests so they can make pledges.",
                          });
                          setPledgeCardModalOpen(false);
                        }
                      }}
                      className="flex-1"
                    >
                      <Share2 className="h-4 w-4 mr-2" />
                      Share Link
                    </Button>
                  )}
                </div>
                {!profile.is_public && (
                  <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <p className="text-sm text-amber-800">
                      💡 Make your profile public in Profile Settings to enable guest access and sharing.
                    </p>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex gap-1 mb-8 bg-gray-100 p-1 rounded-lg">
        <Button
          variant={activeTab === 'overview' ? 'default' : 'ghost'}
          onClick={() => handleTabChange('overview')}
          className="flex-1"
        >
          Overview
        </Button>
        <Button
          variant={activeTab === 'pledges' ? 'default' : 'ghost'}
          onClick={() => handleTabChange('pledges')}
          className="flex-1"
        >
          Manage Pledges
        </Button>
        <Button
          variant={activeTab === 'profile' ? 'default' : 'ghost'}
          onClick={() => handleTabChange('profile')}
          className="flex-1"
        >
          Profile Settings
        </Button>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-8">
          {/* Summary Cards */}
          <div className="grid md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Bride & Groom</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{profile.bride_name} & {profile.groom_name}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Wedding Date</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {profile.wedding_date ? new Date(profile.wedding_date).toLocaleDateString() : 'Not set'}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Venue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{profile.venue || 'Not set'}</div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Pledges */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Recent Pledges</CardTitle>
                  <CardDescription>Latest pledges made by your guests</CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fetchRecentPledgesForProfile}
                  disabled={loading}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {recentPledges.length === 0 ? (
                <p>No recent pledges found.</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Guest Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {recentPledges.map((pledge) => (
                        <tr key={pledge.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{pledge.guest_name}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">UGX {pledge.amount_pledged.toLocaleString()}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(pledge.pledge_date).toLocaleDateString()}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'pledges' && (
        <PledgeManagement
          userId={profile.id}
          onPledgeChange={fetchRecentPledgesForProfile}
        />
      )}

      {activeTab === 'profile' && (
        <ProfileManagement
          profile={profile}
          onProfileUpdated={(updatedProfile) => {
            refreshProfile();
            toast({
              title: "Profile Updated",
              description: "Your changes have been saved successfully.",
            });
          }}
        />
      )}
    </div>
  );
};

export default Dashboard;
