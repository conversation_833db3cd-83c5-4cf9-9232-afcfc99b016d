
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Heart, User, LogOut, Shield } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useAuthModal } from "@/contexts/AuthModalContext";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { isCurrentUserAdmin } from "@/utils/adminAuth";
import MobileNavigation from "@/components/MobileNavigation";

const Header = () => {
  const navigate = useNavigate();
  const { openAuthModal } = useAuthModal();
  const { user, profile, signOut } = useAuth();
  const { toast } = useToast();
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  // Check admin status when user changes
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (user) {
        try {
          const adminStatus = await isCurrentUserAdmin();
          setIsAdmin(adminStatus);
        } catch (error) {
          console.error('Error checking admin status:', error);
          setIsAdmin(false);
        }
      } else {
        setIsAdmin(false);
      }
    };

    checkAdminStatus();
  }, [user]);

  // Debug: Log auth state changes
  console.log('Header render - Auth state:', {
    user: user?.id,
    profile: profile?.id,
    isAdmin,
    hasUser: !!user
  });

  const handleSignOut = async () => {
    if (isSigningOut) return; // Prevent multiple logout attempts

    console.log('Header: Starting sign out process');
    setIsSigningOut(true);
    try {
      console.log('Header: Calling signOut function');
      await signOut();
      console.log('Header: signOut completed successfully');
      toast({
        title: "Signed Out",
        description: "You have been successfully signed out.",
      });
      navigate('/');
    } catch (error) {
      console.error("Header: Sign out error:", error);
      toast({
        title: "Sign Out Error",
        description: "Failed to sign out. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSigningOut(false);
      console.log('Header: Sign out process completed');
    }
  };

  return (
    <header className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        <button
          className="flex items-center gap-2 cursor-pointer bg-transparent border-none p-0"
          onClick={() => navigate('/')}
          aria-label="Go to home page"
        >
          <Heart className="h-8 w-8 text-pink-500" />
          <span className="text-xl font-bold text-gray-800">PFL</span>
        </button>

        <div className="flex items-center gap-4">
          {/* Desktop Navigation - Hidden on mobile */}
          <nav className="hidden md:flex items-center gap-4">
            {user ? (
              <>
                <div className="text-sm text-gray-600">
                  Welcome, {isAdmin ? 'Administrator' : (profile?.treasurer_name || 'User')}
                  {profile && !isAdmin && (
                    <div className="text-xs text-gray-500">
                      {profile.bride_name} & {profile.groom_name}
                    </div>
                  )}
                  {isAdmin && (
                    <div className="text-xs text-blue-600">
                      System Administrator
                    </div>
                  )}
                </div>
                {!isAdmin && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigate('/profile')}
                    aria-label="Go to profile page"
                  >
                    <User className="h-4 w-4 mr-2" />
                    Profile
                  </Button>
                )}
                {isAdmin && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigate('/admin')}
                    aria-label="Go to admin panel"
                    className="text-blue-600 hover:text-blue-700"
                  >
                    <Shield className="h-4 w-4 mr-2" />
                    Admin
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSignOut}
                  disabled={isSigningOut}
                  aria-label="Sign out of account"
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  {isSigningOut ? "Signing Out..." : "Sign Out"}
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="ghost"
                  onClick={() => navigate('/faq')}
                  aria-label="View frequently asked questions"
                >
                  FAQ
                </Button>
                <Button
                  variant="ghost"
                  onClick={() => navigate('/demo-pledge')}
                  aria-label="View demo pledge page"
                >
                  View Demo
                </Button>
                <Button
                  onClick={() => openAuthModal('signin')}
                  aria-label="Sign in to account"
                >
                  Sign In
                </Button>
              </>
            )}
          </nav>

          {/* Mobile Navigation - Visible only on mobile */}
          <MobileNavigation
            isAdmin={isAdmin}
            isSigningOut={isSigningOut}
            onSignOut={handleSignOut}
          />
        </div>
      </div>
    </header>
  );
};

export default Header;
